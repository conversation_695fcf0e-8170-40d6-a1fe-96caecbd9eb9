import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_assets.dart';

class ShimmerLoading extends StatefulWidget {
  final Widget child;
  final bool isLoading;
  final Color? baseColor;
  final Color? highlightColor;

  const ShimmerLoading({
    super.key,
    required this.child,
    required this.isLoading,
    this.baseColor,
    this.highlightColor,
  });

  @override
  State<ShimmerLoading> createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<ShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isLoading) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(ShimmerLoading oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _animationController.repeat();
      } else {
        _animationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) {
      return widget.child;
    }

    final baseColor = widget.baseColor ?? context.cardColor;
    final highlightColor = widget.highlightColor ??
        (context.isDarkMode ? Colors.grey[600]! : Colors.grey[100]!);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

class ShimmerBox extends StatelessWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const ShimmerBox({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: context.isDarkMode ? Colors.grey[700] : Colors.grey[300],
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
    );
  }
}

class PlaceDetailsShimmer extends StatelessWidget {
  const PlaceDetailsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // Hero image shimmer
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: context.backgroundColor,
            elevation: 0,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.cardColor.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.arrow_back_ios_rounded),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: Image.asset(AppAssets.iconsShare),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.favorite_border_rounded),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: ShimmerLoading(
                isLoading: true,
                child: Container(
                  color:
                      context.isDarkMode ? Colors.grey[700] : Colors.grey[300],
                ),
              ),
            ),
          ),

          // Content shimmer
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title section shimmer
                  _buildShimmerCard([
                    const ShimmerBox(width: double.infinity, height: 28),
                    const SizedBox(height: 12),
                    const ShimmerBox(width: 200, height: 16),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const ShimmerBox(
                            width: 80,
                            height: 24,
                            borderRadius:
                                BorderRadius.all(Radius.circular(12))),
                        const SizedBox(width: 12),
                        const ShimmerBox(width: 100, height: 16),
                        const Spacer(),
                        const ShimmerBox(
                            width: 120,
                            height: 24,
                            borderRadius:
                                BorderRadius.all(Radius.circular(12))),
                      ],
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Quick stats shimmer
                  _buildShimmerCard([
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: List.generate(
                          3,
                          (index) => Column(
                                children: [
                                  ShimmerBox(
                                      width: 40,
                                      height: 40,
                                      borderRadius: BorderRadius.circular(20)),
                                  const SizedBox(height: 8),
                                  const ShimmerBox(width: 60, height: 16),
                                  const SizedBox(height: 4),
                                  const ShimmerBox(width: 40, height: 12),
                                ],
                              )),
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Host section shimmer
                  _buildShimmerCard([
                    Row(
                      children: [
                        const ShimmerBox(
                            width: 70,
                            height: 70,
                            borderRadius:
                                BorderRadius.all(Radius.circular(35))),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const ShimmerBox(width: 150, height: 18),
                              const SizedBox(height: 8),
                              const ShimmerBox(width: 100, height: 14),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                            child: ShimmerBox(
                                width: double.infinity,
                                height: 40,
                                borderRadius: BorderRadius.circular(8))),
                        const SizedBox(width: 12),
                        Expanded(
                            child: ShimmerBox(
                                width: double.infinity,
                                height: 40,
                                borderRadius: BorderRadius.circular(8))),
                      ],
                    ),
                  ]),

                  const SizedBox(height: 24),

                  // Description shimmer
                  _buildShimmerCard([
                    const ShimmerBox(width: 120, height: 20),
                    const SizedBox(height: 12),
                    const ShimmerBox(width: double.infinity, height: 16),
                    const SizedBox(height: 8),
                    const ShimmerBox(width: double.infinity, height: 16),
                    const SizedBox(height: 8),
                    const ShimmerBox(width: 250, height: 16),
                  ]),

                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerCard(List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ShimmerLoading(
        isLoading: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        ),
      ),
    );
  }
}
